import os
import signal
from mcculw import ul
from mcculw.enums import ULRange
import numpy as np
import torch
import ctypes
import pandas as pd
import time

# ========== 硬件配置参数 ==========
# 板卡号：DAQ设备的板卡编号，通常从0开始
# 如果系统中有多个DAQ设备，需要指定正确的板卡号
BOARD_NUM = 0          # 板卡号 (0-15)

# 物理通道：DAQ设备上的模拟输入通道编号
# 振动传感器应连接到此通道
CHANNEL = 0            # 物理通道 (0-31)

# 输入电压范围：根据传感器输出范围选择
RANGE = ULRange.BIP10VOLTS  # ±10V范围

# ========== 采集参数设置 ==========
RATE = 12000           # 采样率 Hz
NUM_POINTS = 12000     # 采样点数（默认1秒数据）
# 采样时间 = NUM_POINTS / RATE 秒

# ========== 文件参数 ==========
device_id = "Pump01"
bearing_model = "6205"
rpm = 1797
ball_count = 9
ball_diameter = 7.94     # 单位 mm
pitch_diameter = 39.04   # 单位 mm
contact_angle = 0        # 单位 °

# 指定保存目录
SAVE_DIR = r"C:\Users\<USER>\AppData\Roaming\JetBrains\PyCharmCE2024.2\scratches"

# 创建保存目录（如果不存在）
os.makedirs(SAVE_DIR, exist_ok=True)

# 生成文件名
filename = f"Vibration_{device_id}_{bearing_model}_{rpm}_{RATE}_{ball_count}_{ball_diameter}_{pitch_diameter}_{contact_angle}.xlsx"
filepath = os.path.join(SAVE_DIR, filename)

# 标志变量，用于控制程序终止
running = True

def signal_handler(sig, frame):
    global running
    print("终止信号接收，程序即将退出...")
    running = False

# 注册信号处理函数
signal.signal(signal.SIGINT, signal_handler)

def acquire_data():
    memhandle = ul.win_buf_alloc(NUM_POINTS)
    if not memhandle:
        raise RuntimeError("无法分配内存缓冲区")

    try:
        ul.a_in_scan(BOARD_NUM, CHANNEL, CHANNEL, NUM_POINTS, RATE, RANGE, memhandle, options=0)

        ctypes_array = ctypes.cast(memhandle, ctypes.POINTER(ctypes.c_ushort * NUM_POINTS)).contents
        raw_array = np.frombuffer(ctypes_array, dtype=np.uint16)

        voltage_array = (raw_array.astype(np.float32) / 65535.0) * 20.0 - 10.0

        return torch.tensor(voltage_array, dtype=torch.float32)

    finally:
        ul.win_buf_free(memhandle)


def save_to_excel(signal_tensor):
    # 采样周期（秒）
    time_interval = 1.0 / RATE
    num_points = signal_tensor.shape[0]

    # 时间戳列
    time_stamps = np.arange(num_points) * time_interval

    # 转换为 NumPy 格式
    signal_np = signal_tensor.numpy()

    # 构建 DataFrame：第一列时间，第二列振动信号
    df = pd.DataFrame({
        "Time (s)": time_stamps,
        "Vibration (V)": signal_np
    })

    # 如果文件已存在，则追加（注意要续上时间戳）
    if os.path.exists(filepath):
        existing_df = pd.read_excel(filepath)

        # 续接时间戳
        last_time = existing_df["Time (s)"].iloc[-1]
        time_stamps = time_stamps + last_time + time_interval
        df["Time (s)"] = time_stamps

        df = pd.concat([existing_df, df], ignore_index=True)

    # 保存
    df.to_excel(filepath, index=False)
    print(f"数据已保存至文件：{filepath}")

# ========== 主程序 ==========
if __name__ == "__main__":
    print("开始采集信号，按 Ctrl+C 终止程序...")
    while running:
        signal = acquire_data()
        print(f"采集完成，shape={signal.shape}")
        time.sleep(1)  # 每秒采集一次
        save_to_excel(signal)