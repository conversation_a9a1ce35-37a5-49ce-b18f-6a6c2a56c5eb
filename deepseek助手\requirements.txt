
# ============== 核心数据处理库 ==============
numpy>=1.22.0
scipy>=1.10.0
matplotlib>=3.7.0
pandas>=1.5.0

# ============== 信号处理库 ==============
pywavelets>=1.4.1

# ============== AI API库 ==============
openai>=1.0.0

# ============== 图像处理库 ==============
pillow>=9.5.0
pytesseract>=0.3.10

# ============== 文档处理库 ==============
python-docx>=0.8.11
PyPDF2>=3.0.0

# ============== 数据采集相关库 ==============
# MCC DAQ设备支持（可选）
mcculw>=1.0.0  # MCC Universal Library for Windows

# ============== 可选增强库 ==============
scikit-learn>=1.2.0
PyYAML>=6.0
joblib>=1.2.0
python-dotenv>=1.0.0
tqdm>=4.65.0

# ============== 开发和调试工具 ==============
# 用于代码质量和调试
pytest>=7.0.0
black>=22.0.0
flake8>=5.0.0

# ============== 安装说明 ==============

# 2. 数据采集功能（可选）：
#    pip install mcculw torch
#    注意：mcculw需要在Windows系统上安装MCC DAQ驱动程序
#
# 3. OCR功能需要额外安装Tesseract：
#    Windows: 下载并安装 https://github.com/UB-Mannheim/tesseract/wiki
#    默认安装路径: C:\Program Files\Tesseract-OCR\tesseract.exe
#
# 4. 完整安装：
#    pip install -r requirements.txt