# 数据采集问题修复报告

## 问题描述

根据用户提供的截图，数据采集显示的数据存在问题，显示为大量的科学计数法数值和"nan"值，表明数据采集功能存在严重问题。

## 问题分析

通过对比 `data.acq.py`（正常工作的数据采集程序）和 `deepseek助手/ds-api-debug.py` 中的数据采集代码，发现了以下关键问题：

### 1. 数据类型错误
- **问题**：`ds-api-debug.py` 使用了 `ctypes.c_double` 类型来处理DAQ数据
- **正确做法**：应该使用 `ctypes.c_ushort` 类型，因为MCC DAQ设备返回的是16位无符号整数

### 2. 内存管理错误
- **问题**：直接创建 `ctypes` 数组，没有使用MCC库的内存管理函数
- **正确做法**：使用 `ul.win_buf_alloc()` 分配内存缓冲区，使用 `ul.win_buf_free()` 释放内存

### 3. 电压转换缺失
- **问题**：没有进行ADC原始值到电压值的转换
- **正确做法**：使用公式 `(raw_value / 65535.0) * 20.0 - 10.0` 将16位ADC值转换为±10V电压范围

## 修复内容

### 1. 修复数据采集核心函数 `data_acquisition_worker()`

```python
# 修复前（错误的方法）
data_array = (ctypes.c_double * num_points)()
actual_rate = ul.a_in_scan(board_num, channel, channel, num_points,
                          sample_rate, ul_range, data_array, 0)
signal_data = np.array(data_array)

# 修复后（正确的方法）
memhandle = ul.win_buf_alloc(num_points)
try:
    ul.a_in_scan(board_num, channel, channel, num_points, 
                sample_rate, ul_range, memhandle, options=0)
    
    ctypes_array = ctypes.cast(memhandle, ctypes.POINTER(ctypes.c_ushort * num_points)).contents
    raw_array = np.frombuffer(ctypes_array, dtype=np.uint16)
    signal_data = (raw_array.astype(np.float32) / 65535.0) * 20.0 - 10.0
finally:
    ul.win_buf_free(memhandle)
```

### 2. 增强数据质量检查

添加了更严格的数据质量检查：
- 检查信号是否全为零
- 检查信号是否为常数（标准差过小）
- 检查信号范围是否合理
- 显示信号统计信息（范围、标准差等）

### 3. 添加硬件连接测试功能

新增了 `test_daq_connection()` 函数，提供：
- 板卡连接检测
- 通道配置验证
- 小批量数据采集测试
- 数据质量分析
- 详细的测试报告

### 4. 改进用户界面

- 在数据采集控制面板添加了"🔧 测试连接"按钮
- 提供实时的测试反馈和详细的错误诊断
- 显示更详细的采集状态信息

## 修复效果

修复后的数据采集功能将能够：

1. **正确采集数据**：获得真实的电压值而不是原始ADC计数
2. **显示有意义的数值**：电压值在±10V范围内，不再出现科学计数法或"nan"
3. **提供质量保证**：自动检测数据质量问题并给出警告
4. **便于调试**：通过测试连接功能快速诊断硬件问题

## 使用建议

1. **首次使用前**：点击"🔧 测试连接"按钮验证硬件连接
2. **参数设置**：确保板卡号(BOARD_NUM)和通道号(CHANNEL)设置正确
3. **数据验证**：采集后检查信号范围和统计信息是否合理
4. **故障排除**：如果测试失败，按照测试报告中的建议进行排查

## 技术要点

### DAQ数据采集的正确流程：
1. 使用 `ul.win_buf_alloc()` 分配内存
2. 调用 `ul.a_in_scan()` 进行数据采集
3. 使用正确的数据类型 `ctypes.c_ushort` 访问数据
4. 进行ADC到电压的转换
5. 使用 `ul.win_buf_free()` 释放内存

### 电压转换公式：
```
voltage = (adc_value / 65535.0) * voltage_range - voltage_offset
```
对于±10V范围：`voltage = (adc_value / 65535.0) * 20.0 - 10.0`

这个修复解决了数据采集显示异常数值的根本问题，确保用户能够获得正确的振动信号数据。
