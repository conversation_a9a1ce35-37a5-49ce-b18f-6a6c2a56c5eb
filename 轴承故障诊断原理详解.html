<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能轴承故障诊断系统 - 技术原理详解</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            padding: 30px 0;
            background: linear-gradient(135deg, #0f172a, #1e293b);
            color: white;
            border-radius: 15px 15px 0 0;
            margin: -20px -20px 30px -20px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .section {
            margin-bottom: 40px;
            padding: 25px;
            background: #f8fafc;
            border-radius: 10px;
            border-left: 5px solid #06b6d4;
        }

        .section h2 {
            color: #0f172a;
            font-size: 1.8em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .section h3 {
            color: #1e293b;
            font-size: 1.4em;
            margin: 20px 0 15px 0;
            border-bottom: 2px solid #06b6d4;
            padding-bottom: 5px;
        }

        .icon {
            font-size: 1.2em;
            margin-right: 10px;
        }

        .code-block {
            background: #1e293b;
            color: #f1f5f9;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            border: 1px solid #334155;
        }

        .formula {
            background: #e0f2fe;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #0288d1;
            font-family: 'Times New Roman', serif;
            text-align: center;
            font-size: 1.1em;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ff6b6b;
        }

        .flow-chart {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .flow-step {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            text-align: center;
            flex: 1;
            margin: 5px;
            min-width: 150px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .arrow {
            font-size: 2em;
            color: #06b6d4;
            margin: 0 10px;
        }

        .fault-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .fault-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-top: 4px solid #06b6d4;
        }

        .fault-card h4 {
            color: #0f172a;
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .metric-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #10b981;
        }

        .metric-card h5 {
            color: #0f172a;
            margin-bottom: 8px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .table th, .table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .table th {
            background: #0f172a;
            color: white;
            font-weight: bold;
        }

        .table tr:hover {
            background: #f8fafc;
        }

        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .note::before {
            content: "💡 ";
            font-size: 1.2em;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }

            .header h1 {
                font-size: 2em;
            }

            .flow-chart {
                flex-direction: column;
            }

            .arrow {
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 智能轴承故障诊断系统</h1>
            <p>基于振动信号分析的轴承故障诊断技术原理详解</p>
        </div>

        <div class="section">
            <h2><span class="icon">🎯</span>系统概述</h2>
            <p>本系统是一个集成了<strong>数据采集</strong>、<strong>信号处理</strong>、<strong>特征提取</strong>和<strong>AI诊断</strong>的智能故障诊断平台，专门用于轴承等旋转机械的状态监测和故障预测。</p>

            <div class="highlight">
                <strong>核心优势：</strong>
                <ul>
                    <li>实时数据采集与处理</li>
                    <li>多域信号分析（时域、频域、时频域）</li>
                    <li>基于理论模型的故障识别</li>
                    <li>AI增强的智能诊断</li>
                    <li>专业轴承知识库支持</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="icon">🔄</span>故障诊断流程</h2>
            <div class="flow-chart">
                <div class="flow-step">
                    <strong>1. 数据采集</strong><br>
                    振动信号获取
                </div>
                <div class="arrow">→</div>
                <div class="flow-step">
                    <strong>2. 信号预处理</strong><br>
                    滤波与质量检查
                </div>
                <div class="arrow">→</div>
                <div class="flow-step">
                    <strong>3. 特征提取</strong><br>
                    多域分析
                </div>
                <div class="arrow">→</div>
                <div class="flow-step">
                    <strong>4. 故障识别</strong><br>
                    模式匹配
                </div>
                <div class="arrow">→</div>
                <div class="flow-step">
                    <strong>5. AI诊断</strong><br>
                    智能分析
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="icon">📊</span>数据采集模块</h2>
            <h3>硬件配置</h3>
            <div class="code-block">
# 硬件配置参数
BOARD_NUM = 0          # DAQ板卡号
CHANNEL = 0            # 物理通道号
RANGE = ULRange.BIP10VOLTS  # ±10V电压范围
RATE = 12000           # 采样频率 12kHz
NUM_POINTS = 12000     # 采样点数（1秒数据）
            </div>

            <h3>数据采集核心算法</h3>
            <div class="code-block">
def acquire_data():
    # 1. 分配内存缓冲区
    memhandle = ul.win_buf_alloc(NUM_POINTS)

    try:
        # 2. 执行数据采集
        ul.a_in_scan(BOARD_NUM, CHANNEL, CHANNEL,
                    NUM_POINTS, RATE, RANGE, memhandle, options=0)

        # 3. 数据类型转换
        ctypes_array = ctypes.cast(memhandle,
                      ctypes.POINTER(ctypes.c_ushort * NUM_POINTS)).contents
        raw_array = np.frombuffer(ctypes_array, dtype=np.uint16)

        # 4. ADC值转电压值
        voltage_array = (raw_array.astype(np.float32) / 65535.0) * 20.0 - 10.0

        return torch.tensor(voltage_array, dtype=torch.float32)
    finally:
        # 5. 释放内存
        ul.win_buf_free(memhandle)
            </div>

            <div class="note">
                <strong>关键技术点：</strong>ADC原始值必须转换为实际电压值，转换公式为：<br>
                <code>电压 = (ADC值 / 65535) × 20V - 10V</code>
            </div>
        </div>

        <div class="section">
            <h2><span class="icon">🔬</span>信号分析理论基础</h2>

            <h3>轴承故障特征频率计算</h3>
            <p>轴承故障诊断的核心是识别特定的故障特征频率。根据轴承几何参数和运行条件，可以计算出四种主要故障频率：</p>

            <div class="formula">
                <strong>外圈故障频率 (BPFO)：</strong><br>
                f<sub>BPFO</sub> = (N<sub>b</sub>/2) × f<sub>r</sub> × (1 - (D<sub>b</sub>/D<sub>p</sub>) × cos α)
            </div>

            <div class="formula">
                <strong>内圈故障频率 (BPFI)：</strong><br>
                f<sub>BPFI</sub> = (N<sub>b</sub>/2) × f<sub>r</sub> × (1 + (D<sub>b</sub>/D<sub>p</sub>) × cos α)
            </div>

            <div class="formula">
                <strong>滚动体故障频率 (BSF)：</strong><br>
                f<sub>BSF</sub> = (D<sub>p</sub>/(2×D<sub>b</sub>)) × f<sub>r</sub> × (1 - ((D<sub>b</sub>/D<sub>p</sub>) × cos α)²)
            </div>

            <div class="formula">
                <strong>保持架故障频率 (FTF)：</strong><br>
                f<sub>FTF</sub> = (f<sub>r</sub>/2) × (1 - (D<sub>b</sub>/D<sub>p</sub>) × cos α)
            </div>

            <div class="note">
                其中：N<sub>b</sub>=滚动体数量，f<sub>r</sub>=轴旋转频率，D<sub>b</sub>=滚动体直径，D<sub>p</sub>=节径，α=接触角
            </div>

            <h3>程序中的实现</h3>
            <div class="code-block">
def calculate_bearing_fault_frequencies(self, rpm, ball_num, ball_diameter,
                                       pitch_diameter, contact_angle):
    # 轴旋转频率 (Hz)
    shaft_freq = rpm / 60.0
    cos_alpha = np.cos(np.radians(contact_angle))

    # 计算各故障特征频率
    bpfo = (ball_num / 2) * shaft_freq * (1 - (ball_diameter / pitch_diameter) * cos_alpha)
    bpfi = (ball_num / 2) * shaft_freq * (1 + (ball_diameter / pitch_diameter) * cos_alpha)
    bsf = (pitch_diameter / (2 * ball_diameter)) * shaft_freq * \
          (1 - ((ball_diameter / pitch_diameter) * cos_alpha)**2)
    ftf = (shaft_freq / 2) * (1 - (ball_diameter / pitch_diameter) * cos_alpha)

    return {'BPFO': bpfo, 'BPFI': bpfi, 'BSF': bsf, 'FTF': ftf}
            </div>
        </div>

        <div class="section">
            <h2><span class="icon">📈</span>多域信号分析</h2>

            <h3>1. 时域分析</h3>
            <p>时域分析通过统计指标识别信号中的冲击特征，是故障检测的第一步。</p>

            <div class="metrics-grid">
                <div class="metric-card">
                    <h5>峭度 (Kurtosis)</h5>
                    <p><strong>正常值：</strong>≈ 3</p>
                    <p><strong>故障阈值：</strong>> 4</p>
                    <p>描述信号分布的尖锐程度，对早期故障敏感</p>
                </div>

                <div class="metric-card">
                    <h5>峰值因子 (CF)</h5>
                    <p><strong>正常值：</strong>3-4</p>
                    <p><strong>故障阈值：</strong>> 5</p>
                    <p>峰值与RMS的比值，反映冲击强度</p>
                </div>

                <div class="metric-card">
                    <h5>脉冲因子 (IF)</h5>
                    <p><strong>正常值：</strong>4-5</p>
                    <p><strong>故障阈值：</strong>> 6</p>
                    <p>峰值与平均值的比值，比CF更敏感</p>
                </div>

                <div class="metric-card">
                    <h5>RMS值</h5>
                    <p><strong>作用：</strong>能量水平</p>
                    <p><strong>特点：</strong>反映故障严重程度</p>
                    <p>均方根值，表示振动总能量</p>
                </div>
            </div>

            <div class="code-block">
def calculate_time_domain_metrics(self, signal):
    # 基本统计量
    rms = np.sqrt(np.mean(signal**2))
    peak = np.max(np.abs(signal))
    mean_abs = np.mean(np.abs(signal))

    # 高阶统计量
    kurtosis = scipy.stats.kurtosis(signal, fisher=False)
    skewness = scipy.stats.skew(signal)

    # 形状因子
    crest_factor = peak / rms if rms > 0 else 0
    impulse_factor = peak / mean_abs if mean_abs > 0 else 0

    return {
        'RMS': rms, 'Peak': peak, 'Kurtosis': kurtosis,
        'Crest_Factor': crest_factor, 'Impulse_Factor': impulse_factor
    }
            </div>

            <h3>2. 频域分析（包络谱）</h3>
            <p>包络谱分析是轴承故障诊断的核心技术，通过希尔伯特变换提取信号包络，再进行FFT分析。</p>

            <div class="code-block">
def calculate_envelope_spectrum(self, signal, fs):
    # 1. 高通滤波（去除低频干扰）
    sos = scipy.signal.butter(4, 1000, btype='high', fs=fs, output='sos')
    filtered_signal = scipy.signal.sosfilt(sos, signal)

    # 2. 希尔伯特变换获取解析信号
    analytic_signal = scipy.signal.hilbert(filtered_signal)

    # 3. 计算包络（幅值）
    envelope = np.abs(analytic_signal)

    # 4. 包络信号的FFT
    envelope_fft = np.fft.fft(envelope)
    envelope_spectrum = np.abs(envelope_fft[:len(envelope)//2])

    # 5. 频率轴
    freq_axis = np.fft.fftfreq(len(envelope), 1/fs)[:len(envelope)//2]

    return freq_axis, envelope_spectrum
            </div>

            <h3>3. 时频分析（连续小波变换）</h3>
            <p>小波变换提供时间-频率联合分析，能够识别非平稳信号的瞬态特征。</p>

            <div class="code-block">
def perform_wavelet_transform(self, signal, wavelet='morl', level=4, fs=12000):
    # 1. 确定分析尺度
    max_scale = min(len(signal) // 10, 64)
    scales = np.logspace(1, np.log10(max_scale), level)

    # 2. 连续小波变换
    coefficients, frequencies = pywt.cwt(signal, scales, wavelet, 1/fs)

    # 3. 计算小波功率谱
    power = np.abs(coefficients)**2

    return coefficients, scales, frequencies, power
            </div>
        </div>

        <div class="section">
            <h2><span class="icon">🎯</span>故障识别算法</h2>

            <h3>基于理论模型的故障匹配</h3>
            <p>系统通过比较包络谱中的峰值频率与理论故障特征频率来识别故障类型。</p>

            <div class="code-block">
def apply_theoretical_model_with_params(self, freq_axis, env_spectrum,
                                      fault_freqs, threshold=0.05):
    results = []
    # 找出包络谱中的前5个最大峰值
    peak_indices = np.argsort(-env_spectrum)[:5]
    found_faults = []

    for idx in peak_indices:
        peak_f = freq_axis[idx]
        # 与各故障特征频率比较
        for name, f_val in fault_freqs.items():
            # 基频匹配
            if abs(peak_f - f_val) / f_val < threshold:
                found_faults.append((peak_f, name, "基频"))
            # 谐波匹配（2-4次谐波）
            for harmonic in range(2, 5):
                if abs(peak_f - f_val * harmonic) / (f_val * harmonic) < threshold:
                    found_faults.append((peak_f, name, f"{harmonic}次谐波"))

    return found_faults
            </div>

            <h3>故障严重程度评估</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>严重程度</th>
                        <th>信噪比</th>
                        <th>谐波特征</th>
                        <th>时域指标</th>
                        <th>剩余寿命</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>轻微故障</strong></td>
                        <td>&lt; 3dB</td>
                        <td>仅有基频</td>
                        <td>峭度 4-6</td>
                        <td>3-6个月</td>
                    </tr>
                    <tr>
                        <td><strong>中度故障</strong></td>
                        <td>3-10dB</td>
                        <td>2-3个谐波</td>
                        <td>峭度 6-10</td>
                        <td>1-3个月</td>
                    </tr>
                    <tr>
                        <td><strong>严重故障</strong></td>
                        <td>&gt; 10dB</td>
                        <td>多谐波+边带</td>
                        <td>峭度 &gt; 10</td>
                        <td>立即更换</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2><span class="icon">🤖</span>AI增强诊断</h2>

            <h3>DeepSeek API集成</h3>
            <p>系统集成了DeepSeek大语言模型，结合专业轴承知识库提供智能诊断建议。</p>

            <div class="code-block">
# AI诊断流程
def ai_diagnosis(self, analysis_data):
    # 1. 构建专业提示词
    system_prompt = get_bearing_knowledge_base()

    # 2. 整合分析结果
    user_message = f"""
    振动信号分析结果：
    - 时域指标：{analysis_data['time_metrics']}
    - 频域特征：{analysis_data['freq_features']}
    - 故障频率匹配：{analysis_data['fault_matching']}
    - 小波分析：{analysis_data['wavelet_features']}

    请基于以上数据提供专业的故障诊断建议。
    """

    # 3. 调用DeepSeek API
    response = self.client.chat.completions.create(
        model="deepseek-chat",
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ],
        stream=True  # 流式输出
    )

    return response
            </div>

            <h3>专业知识库支持</h3>
            <div class="highlight">
                <strong>知识库内容包括：</strong>
                <ul>
                    <li>轴承结构与类型详解（深沟球轴承、角接触球轴承等）</li>
                    <li>故障模式分析（疲劳剥落、磨损、裂纹、腐蚀等）</li>
                    <li>振动特征解读（调制特性、发展过程、典型案例）</li>
                    <li>诊断标准与规范（ISO、GB、ANSI等国际标准）</li>
                    <li>维护建议与预防措施</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="icon">🔍</span>四种主要故障类型特征</h2>

            <div class="fault-types">
                <div class="fault-card">
                    <h4>🔴 内圈故障 (BPFI)</h4>
                    <p><strong>振动特征：</strong></p>
                    <ul>
                        <li>高频冲击，峭度值显著增大(>4)</li>
                        <li>被轴旋转频率调制，有边带</li>
                        <li>小波图中3-8尺度有断续能量条带</li>
                    </ul>
                    <p><strong>典型原因：</strong>疲劳剥落、润滑不良、过载</p>
                </div>

                <div class="fault-card">
                    <h4>🟡 外圈故障 (BPFO)</h4>
                    <p><strong>振动特征：</strong></p>
                    <ul>
                        <li>稳定的周期性冲击</li>
                        <li>峰值因子和脉冲因子增大</li>
                        <li>小波图中4-10尺度有规律能量分布</li>
                    </ul>
                    <p><strong>典型原因：</strong>安装不当、轴承座变形、腐蚀</p>
                </div>

                <div class="fault-card">
                    <h4>🟢 滚动体故障 (BSF)</h4>
                    <p><strong>振动特征：</strong></p>
                    <ul>
                        <li>调幅特性，冲击间隔不均匀</li>
                        <li>被保持架频率(FTF)调制</li>
                        <li>多尺度交替能量集中</li>
                    </ul>
                    <p><strong>典型原因：</strong>材料缺陷、不均匀磨损、椭圆变形</p>
                </div>

                <div class="fault-card">
                    <h4>🔵 保持架故障 (FTF)</h4>
                    <p><strong>振动特征：</strong></p>
                    <ul>
                        <li>低频调制特性</li>
                        <li>振动幅值较小但有规律</li>
                        <li>大尺度区域(10-16)有能量分布</li>
                    </ul>
                    <p><strong>典型原因：</strong>断裂、变形、材料劣化、松动</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="icon">📋</span>诊断报告示例</h2>

            <div class="code-block">
=== 轴承故障诊断报告 ===
设备信息：6205轴承，转速1797rpm，9个滚动体

时域分析结果：
- RMS值：2.34 (正常范围)
- 峭度：8.7 (异常，>4)
- 峰值因子：6.2 (异常，>5)

频域分析结果：
- 检测到144.2Hz峰值，匹配内圈故障频率BPFI(144.19Hz)
- 检测到288.4Hz峰值，为BPFI的2次谐波
- 信噪比：12dB (严重故障级别)

小波分析结果：
- 在尺度5-7区域检测到周期性能量条带
- 能量分布呈断续特征，符合内圈故障模式

AI诊断建议：
根据分析结果，该轴承存在明显的内圈故障特征。建议：
1. 立即安排停机检查
2. 更换轴承并检查润滑系统
3. 检查轴的对中情况
4. 加强后续监测频次
            </div>
        </div>

        <div class="section">
            <h2><span class="icon">⚙️</span>系统技术特点</h2>

            <div class="highlight">
                <strong>技术优势：</strong>
                <ul>
                    <li><strong>多域融合分析：</strong>时域、频域、时频域三重验证</li>
                    <li><strong>理论模型驱动：</strong>基于轴承几何参数的精确计算</li>
                    <li><strong>AI智能增强：</strong>大语言模型提供专业诊断建议</li>
                    <li><strong>实时处理能力：</strong>支持在线监测和离线分析</li>
                    <li><strong>专业知识库：</strong>涵盖轴承全生命周期管理</li>
                    <li><strong>可视化展示：</strong>直观的图表和交互式分析</li>
                </ul>
            </div>

            <div class="note">
                <strong>应用场景：</strong>适用于电机、泵、风机、压缩机、齿轮箱等各类旋转机械的轴承状态监测，支持预测性维护和故障预警。
            </div>
        </div>

        <div style="text-align: center; padding: 30px; background: #0f172a; color: white; border-radius: 10px; margin-top: 30px;">
            <h3>🎓 技术总结</h3>
            <p>本系统通过<strong>信号采集 → 多域分析 → 特征匹配 → AI诊断</strong>的完整流程，</p>
            <p>实现了轴承故障的<strong>早期发现、准确识别、严重程度评估</strong>和<strong>智能诊断建议</strong>，</p>
            <p>为设备维护提供了科学、可靠的技术支撑。</p>
        </div>
    </div>
</body>
</html>
